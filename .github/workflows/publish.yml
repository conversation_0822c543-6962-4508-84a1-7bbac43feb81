name: Publish to <PERSON><PERSON><PERSON>

on:
  release:
    types: [published]
  workflow_dispatch:  # Allows manual triggering

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
        pip install -e .
    - name: Run tests
      run: |
        # Add current directory to PYTHONPATH
        PYTHONPATH=$PYTHONPATH:$(pwd) pytest

  build-and-publish:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: python -m build

    - name: Check distribution
      run: twine check dist/*

    - name: Publish to Test PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.TEST_PYPI_API_TOKEN }}
        TWINE_REPOSITORY_URL: https://test.pypi.org/legacy/
      run: twine upload --skip-existing dist/*

    - name: Publish to PyPI
      if: github.event_name == 'release' && github.event.release.prerelease == false
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: twine upload dist/*
